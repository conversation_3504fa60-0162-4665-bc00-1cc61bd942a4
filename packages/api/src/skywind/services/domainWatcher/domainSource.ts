import { getStaticDomainPoolService } from "../staticDomainPool";
import { getDynamicDomainPoolService } from "../dynamicDomainPool";
import type { DomainStatus } from "./types";
import { DynamicDomainService, getDynamicDomainService, getStaticDomainService, StaticDomainService } from "../domain";

export interface DomainSource {
    setBlocked(status: DomainStatus, adapterId: string): Promise<void>;
}

export class StaticPoolDomainSource implements DomainSource {
    constructor(private readonly poolId: number, private readonly domainId: number) {
    }

    public async setBlocked(status: DomainStatus) {
        await getStaticDomainPoolService().setBlocked(this.poolId, this.domainId, status.lastCheckedAt);
    }
}

export class DynamicPoolDomainSource implements DomainSource {
    constructor(private readonly poolId: number, private readonly domainId: number) {
    }

    public async setBlocked(status: DomainStatus) {
        await getDynamicDomainPoolService().setBlocked(this.poolId, this.domainId, status.lastCheckedAt);
    }
}

export class StaticDomainSource implements DomainSource {
    private readonly service: StaticDomainService;

    constructor(private readonly domainId: number) {
        this.service = getStaticDomainService();
    }

    public async setBlocked(status: DomainStatus, adapterId: string) {
        await this.service.update({ id: this.domainId, info: { monitoringStatus: { [adapterId]: status } } });
    }
}

export class DynamicDomainSource implements DomainSource {
    private readonly service: DynamicDomainService;

    constructor(private readonly domainId: number) {
        this.service = getDynamicDomainService();
    }

    public async setBlocked(status: DomainStatus, adapterId: string) {
        await this.service.update({ id: this.domainId, info: { monitoringStatus: { [adapterId]: status } } });
    }
}
